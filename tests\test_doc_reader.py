import unittest
from unittest.mock import patch, MagicMock
from document_processor.doc_reader import DocumentReader
from docx import Document
from docx.text.paragraph import Paragraph
from docx.shared import Pt

class TestDocReader(unittest.TestCase):
    def setUp(self):
        self.reader = DocumentReader()
        
    def create_mock_document(self, has_toc: bool):
        """创建模拟DOCX文档"""
        mock_doc = MagicMock(spec=Document)
        mock_doc.paragraphs = []
        
        # 添加标题1
        p1 = self.create_mock_paragraph("第一章 概述", "Heading 1")
        mock_doc.paragraphs.append(p1)
        
        if has_toc:
            # 添加目录标题
            toc_title = self.create_mock_paragraph("目录", "Heading 2")
            mock_doc.paragraphs.append(toc_title)
            
            # 添加目录内容
            toc_item1 = self.create_mock_paragraph("1.1 背景.............1", "Normal")
            toc_item2 = self.create_mock_paragraph("1.2 目标.............2", "Normal")
            mock_doc.paragraphs.extend([toc_item1, toc_item2])
        
        # 添加正文标题和内容
        section_title = self.create_mock_paragraph("1.1 背景", "Heading 3")
        content1 = self.create_mock_paragraph("这是正文内容1", "Normal")
        content2 = self.create_mock_paragraph("这是正文内容2", "Normal")
        
        mock_doc.paragraphs.extend([section_title, content1, content2])
        return mock_doc
    
    def create_mock_paragraph(self, text, style):
        """创建模拟段落"""
        p = MagicMock(spec=Paragraph)
        p.text = text
        p.style.name = style
        return p
        
    def test_document_with_toc(self):
        """测试带目录的文档"""
        mock_doc = self.create_mock_document(has_toc=True)
        
        with patch('docx.Document', return_value=mock_doc):
            doc_info = self.reader.read_document("tests/dummy.docx")
            
        # 验证目录内容被跳过
        content = doc_info['content']
        self.assertNotIn("1.1 背景.............1", content)
        self.assertNotIn("1.2 目标.............2", content)
        
        # 验证正文内容保留
        self.assertIn("这是正文内容1", content)
        self.assertIn("1.1 背景", content)  # 标题保留
        
    def test_document_without_toc(self):
        """测试不带目录的文档"""
        mock_doc = self.create_mock_document(has_toc=False)
        
        with patch('docx.Document', return_value=mock_doc):
            doc_info = self.reader.read_document("tests/dummy.docx")
            
        # 验证所有内容都被保留
        content = doc_info['content']
        self.assertIn("第一章 概述", content)
        self.assertIn("1.1 背景", content)
        self.assertIn("这是正文内容1", content)
        
    def test_toc_variations(self):
        """测试不同格式的目录标题"""
        variations = ["目录", "目录 ", " 目录", "Contents", "Table of Contents"]
        
        for toc_title in variations:
            mock_doc = MagicMock(spec=Document)
            mock_doc.paragraphs = []
            
            # 添加目录标题和内容
            toc_p = self.create_mock_paragraph(toc_title, "Heading 2")
            content_p = self.create_mock_paragraph("目录内容", "Normal")
            mock_doc.paragraphs.extend([toc_p, content_p])
            
            with patch('docx.Document', return_value=mock_doc):
                doc_info = self.reader.read_document("tests/dummy.docx")
                
            # 验证目录内容被跳过
            self.assertNotIn("目录内容", doc_info['content'])
            
    def test_content_after_toc(self):
        """测试目录后的内容被正确保留"""
        mock_doc = self.create_mock_document(has_toc=True)
        
        # 在目录后添加额外内容
        extra_content = self.create_mock_paragraph("重要内容", "Normal") 
        mock_doc.paragraphs.append(extra_content)
        
        with patch('docx.Document', return_value=mock_doc):
            doc_info = self.reader.read_document("tests/dummy.docx")
            
        # 验证重要内容被保留
        self.assertIn("重要内容", doc_info['content'])

if __name__ == '__main__':
    unittest.main()